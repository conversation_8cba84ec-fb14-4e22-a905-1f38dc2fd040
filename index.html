<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Weather App</title>
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
</head>
<body class="bg-gray-900 text-white font-sans leading-normal tracking-wide min-h-screen flex items-center justify-center flex-col ">
  <div class="weather-container bg-gradient-to-br from-blue-300 to-blue-500 p-8 rounded-lg shadow-lg max-w-3xl mx-auto mt-10 mb-10 display-flex justify-center items-center flex-col">
    <h1 class="text-3xl font-bold text-center mt-8 mb-8 text-white">Weather App</h1>
    <form id="search-form" class="relative">
      <input class="bg-white text-gray-900 rounded-lg p-2 pr-12 w-full mb-4 text-lg font-bold focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50" type="text" id="city-input" placeholder="Enter city name" required>
      <button type="submit" class="absolute right-3 top-2 text-gray-600 hover:text-blue-500">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m21 21-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
      </button>
    </form>
    <div id="weather-info" class="text-center text-2xl font-bold mt-8 mb-8 text-white"></div>
    <div id="forecast" class="text-center text-2xl font-bold mt-8 mb-8 text-white"></div>
  </div>
  <script src="script.js"></script>
</body>
</html>
