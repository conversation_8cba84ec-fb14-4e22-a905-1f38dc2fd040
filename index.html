<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Weather App</title>
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  <style>
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
    .animate-spin {
      animation: spin 1s linear infinite;
    }
    .transition-all {
      transition-property: all;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 150ms;
    }
    .duration-200 {
      transition-duration: 200ms;
    }
    /* Hide scrollbar for horizontal scroll */
    .scrollbar-hide {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
    .scrollbar-hide::-webkit-scrollbar {
      display: none;
    }
    /* Smooth scrolling */
    .scroll-smooth {
      scroll-behavior: smooth;
    }
    /* Enhanced touch scrolling */
    .scrollbar-hide {
      -webkit-overflow-scrolling: touch;
      scroll-snap-type: x mandatory;
      overscroll-behavior-x: contain;
      scroll-behavior: smooth;
    }
    /* Prevent text selection during drag */
    .scrollbar-hide * {
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
    }
    /* Smooth scroll container */
    #forecast-scroll-container {
      scroll-behavior: smooth;
      -webkit-overflow-scrolling: touch;
      scroll-snap-type: x mandatory;
      scroll-snap-stop: always;
    }
    /* Smooth scroll cards */
    .forecast-card {
      scroll-snap-align: start;
      scroll-snap-stop: always;
    }
  </style>
</head>
<body class="bg-gray-900 text-white font-sans leading-normal tracking-wide min-h-screen flex items-center justify-center p-4">
  <div class="weather-container bg-gradient-to-br from-blue-300 to-blue-500 p-4 sm:p-6 md:p-8 rounded-lg shadow-lg w-full max-w-xs sm:max-w-md md:max-w-2xl lg:max-w-3xl mx-auto my-4 sm:my-6 md:my-10 flex justify-center items-center flex-col">
    <h1 class="text-2xl sm:text-3xl md:text-4xl font-bold text-center mt-4 sm:mt-6 md:mt-8 mb-4 sm:mb-6 md:mb-8 text-white">Weather App</h1>
    <form id="search-form" class="relative w-full max-w-md">
      <input class="bg-white text-gray-900 rounded-lg p-3 pr-12 w-full mb-4 text-base sm:text-lg font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 shadow-md"
             type="text"
             id="city-input"
             placeholder="Enter city name (e.g., London, New York)"
             required
             autocomplete="off"
             aria-label="City name input">
      <button type="submit"
              class="absolute right-3 top-3 text-gray-600 hover:text-blue-500 transition-colors duration-200"
              aria-label="Search weather">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m21 21-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
      </button>
    </form>
    <div id="weather-info" class="text-center text-lg sm:text-xl md:text-2xl font-bold mt-4 sm:mt-6 md:mt-8 mb-4 sm:mb-6 md:mb-8 text-white w-full"></div>
    <div id="forecast" class="text-center text-lg sm:text-xl md:text-2xl font-bold mt-4 sm:mt-6 md:mt-8 mb-4 sm:mb-6 md:mb-8 text-white w-full"></div>
  </div>
  <script src="script.js"></script>
</body>
</html>
