// Weather App Configuration
const CONFIG = {
  apiKey: 'cce3d0c05c88c1f0029da5f7e6c56f5d',
  baseUrl: 'https://api.openweathermap.org/data/2.5',
  units: 'metric'
};

// DOM Elements
const elements = {
  searchForm: document.getElementById('search-form'),
  cityInput: document.getElementById('city-input'),
  weatherInfo: document.getElementById('weather-info'),
  forecastEl: document.getElementById('forecast')
};

// Weather App Class
class WeatherApp {
  constructor() {
    this.initEventListeners();
  }

  initEventListeners() {
    elements.searchForm.addEventListener('submit', (e) => this.handleSearch(e));
  }

  async handleSearch(e) {
    e.preventDefault();
    const city = elements.cityInput.value.trim();

    if (!this.validateInput(city)) return;

    this.showLoading();

    try {
      const [weatherData, forecastData] = await Promise.all([
        this.fetchWeatherData(city),
        this.fetchForecastData(city)
      ]);

      this.displayWeather(weatherData);
      this.displayForecast(forecastData);
    } catch (error) {
      this.handleError(error);
    }
  }

  validateInput(city) {
    if (city.length === 0) {
      this.showError('Please enter a city name');
      return false;
    }
    if (city.length < 2) {
      this.showError('City name must be at least 2 characters long');
      return false;
    }
    return true;
  }

  showLoading() {
    elements.weatherInfo.innerHTML = `
      <div class="flex items-center justify-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white mr-3"></div>
        <span>Loading weather data...</span>
      </div>
    `;
    elements.forecastEl.innerHTML = '';
  }

  async fetchWeatherData(city) {
    const response = await fetch(
      `${CONFIG.baseUrl}/weather?q=${encodeURIComponent(city)}&appid=${CONFIG.apiKey}&units=${CONFIG.units}`
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `City "${city}" not found`);
    }

    return response.json();
  }

  async fetchForecastData(city) {
    const response = await fetch(
      `${CONFIG.baseUrl}/forecast?q=${encodeURIComponent(city)}&appid=${CONFIG.apiKey}&units=${CONFIG.units}`
    );

    if (!response.ok) {
      throw new Error('Forecast data not available');
    }

    return response.json();
  }

  displayWeather(weatherData) {
    const { name, sys, weather, main, wind } = weatherData;

    elements.weatherInfo.innerHTML = `
      <div class="mb-4 px-2">
        <!-- City Name - Mobile Responsive -->
        <div class="text-2xl sm:text-3xl md:text-4xl font-bold text-center mb-4">${name}, ${sys.country}</div>

        <!-- Main Weather Display - Mobile Responsive -->
        <div class="flex flex-col sm:flex-row items-center justify-center gap-4 mb-4">
          <img class="h-16 sm:h-20 md:h-24"
               src="https://openweathermap.org/img/wn/${weather[0].icon}@4x.png"
               alt="${weather[0].description}"
               onerror="this.style.display='none'">
          <div class="text-center sm:text-left">
            <div class="text-4xl sm:text-5xl md:text-6xl font-extrabold">${Math.round(main.temp)}°C</div>
            <div class="text-lg sm:text-xl capitalize text-blue-100">${weather[0].description}</div>
          </div>
        </div>

        <!-- Weather Details - Mobile Responsive Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-4 text-center text-sm sm:text-base">
          <div class="bg-blue-400 bg-opacity-20 rounded-lg p-2 sm:p-3">
            <div class="text-blue-200 text-xs">Humidity</div>
            <div class="font-bold">💧 ${main.humidity}%</div>
          </div>
          <div class="bg-blue-400 bg-opacity-20 rounded-lg p-2 sm:p-3">
            <div class="text-blue-200 text-xs">Wind Speed</div>
            <div class="font-bold">🌪️ ${Math.round(wind?.speed || 0)} m/s</div>
          </div>
          <div class="bg-blue-400 bg-opacity-20 rounded-lg p-2 sm:p-3">
            <div class="text-blue-200 text-xs">Feels Like</div>
            <div class="font-bold">🌡️ ${Math.round(main.feels_like)}°C</div>
          </div>
        </div>
      </div>
    `;
  }

  displayForecast(forecastData) {
    const dailyForecasts = this.processForecastData(forecastData.list);

    elements.forecastEl.innerHTML = `
      <div class="px-2">
        <h2 class="text-xl sm:text-2xl font-bold mb-4 text-center">5-Day Forecast</h2>

        <!-- Horizontal Scrolling Container - Samsung Style -->
        <div class="relative">
          <div class="flex overflow-x-auto scrollbar-hide gap-3 pb-2 snap-x snap-mandatory" style="scrollbar-width: none; -ms-overflow-style: none;">
            ${dailyForecasts.map(forecast => this.createForecastCard(forecast)).join('')}
          </div>

          <!-- Scroll Indicator -->
          <div class="flex justify-center mt-2">
            <div class="text-blue-200 text-xs">← Swipe to see more days →</div>
          </div>
        </div>
      </div>
    `;
  }

  processForecastData(forecastList) {
    const daily = [];
    let lastDate = '';

    for (const entry of forecastList) {
      const dt = new Date(entry.dt_txt);
      const dateString = dt.toDateString();

      // Get midday forecast for each day
      if (dt.getHours() === 12 && dateString !== lastDate) {
        daily.push(entry);
        lastDate = dateString;
        if (daily.length === 5) break;
      }
    }

    return daily;
  }

  createForecastCard(forecast) {
    const date = new Date(forecast.dt_txt);
    const dayName = date.toLocaleDateString(undefined, {
      weekday: 'short'
    });
    const dayDate = date.toLocaleDateString(undefined, {
      month: 'short',
      day: 'numeric'
    });

    return `
      <div class="flex-shrink-0 bg-blue-400 bg-opacity-30 rounded-xl p-3 sm:p-4 w-24 sm:w-28 md:w-32 text-center hover:bg-opacity-50 transition-all duration-200 snap-start">
        <!-- Day Name -->
        <div class="font-bold text-xs sm:text-sm text-blue-100 mb-1">${dayName}</div>
        <div class="text-xs text-blue-200 mb-2">${dayDate}</div>

        <!-- Weather Icon -->
        <img src="https://openweathermap.org/img/wn/${forecast.weather[0].icon}@2x.png"
             alt="${forecast.weather[0].main}"
             class="mx-auto w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 mb-2"
             onerror="this.style.display='none'">

        <!-- Temperature -->
        <div class="font-bold text-sm sm:text-base md:text-lg text-white mb-1">${Math.round(forecast.main.temp)}°C</div>

        <!-- High/Low Temps -->
        <div class="text-xs text-blue-200">
          <span class="block">H: ${Math.round(forecast.main.temp_max)}°</span>
          <span class="block">L: ${Math.round(forecast.main.temp_min)}°</span>
        </div>

        <!-- Weather Description (hidden on very small screens) -->
        <div class="hidden sm:block text-xs capitalize text-blue-100 mt-1 leading-tight">${forecast.weather[0].description}</div>
      </div>
    `;
  }

  handleError(error) {
    console.error('Weather App Error:', error);

    let errorMessage = 'An unexpected error occurred';

    if (error.message.includes('not found')) {
      errorMessage = 'City not found. Please check the spelling and try again.';
    } else if (error.message.includes('network')) {
      errorMessage = 'Network error. Please check your internet connection.';
    } else if (error.message) {
      errorMessage = error.message;
    }

    this.showError(errorMessage);
  }

  showError(message) {
    elements.weatherInfo.innerHTML = `
      <div class="text-red-400 bg-red-100 bg-opacity-20 rounded-lg p-4">
        <div class="flex items-center">
          <span class="text-2xl mr-2">⚠️</span>
          <span>${message}</span>
        </div>
      </div>
    `;
    elements.forecastEl.innerHTML = '';
  }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new WeatherApp();
});

// Add some utility functions for better UX
elements.cityInput.addEventListener('keyup', (e) => {
  if (e.key === 'Enter') {
    elements.searchForm.dispatchEvent(new Event('submit'));
  }
});

// Clear previous results when user starts typing
elements.cityInput.addEventListener('input', () => {
  if (elements.cityInput.value.trim() === '') {
    elements.weatherInfo.innerHTML = '';
    elements.forecastEl.innerHTML = '';
  }
});
