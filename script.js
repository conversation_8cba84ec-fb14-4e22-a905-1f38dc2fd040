const apiKey = 'cce3d0c05c88c1f0029da5f7e6c56f5d'; 
const searchForm = document.getElementById('search-form');
const cityInput = document.getElementById('city-input');
const weatherInfo = document.getElementById('weather-info');
const forecastEl = document.getElementById('forecast');

searchForm.addEventListener('submit', async (e) => {
  e.preventDefault();
  const city = cityInput.value.trim();
  if (city.length === 0) return;

  weatherInfo.textContent = 'Loading...';
  forecastEl.textContent = '';

  try {

    const weatherRes = await fetch(
      `https://api.openweathermap.org/data/2.5/weather?q=${encodeURIComponent(
        city
      )}&appid=${apiKey}&units=metric`
    );
    if (!weatherRes.ok) throw new Error('City not found');
    const weatherData = await weatherRes.json();

    weatherInfo.innerHTML = `
      <div class="mb-4">
        <span class="text-4xl">${weatherData.name}, ${weatherData.sys.country}</span>
        <div class="flex items-center justify-center">
          <img class="h-20 inline" src="https://openweathermap.org/img/wn/${
            weatherData.weather[0].icon
          }@4x.png" alt="${weatherData.weather[0].description}">
          <span class="text-6xl font-extrabold ml-2">${Math.round(
            weatherData.main.temp
          )}°C</span>
        </div>
        <div class="text-xl capitalize">${weatherData.weather[0].description}</div>
        <div>Humidity: ${weatherData.main.humidity}% | Wind: ${Math.round(
      weatherData.wind.speed
    )} m/s</div>
      </div>
    `;

    const forecastRes = await fetch(
      `https://api.openweathermap.org/data/2.5/forecast?q=${encodeURIComponent(
        city
      )}&appid=${apiKey}&units=metric`
    );
    if (!forecastRes.ok) throw new Error("Forecast not found");
    const forecastData = await forecastRes.json();

    const daily = [];
    let lastDate = '';
    for (let entry of forecastData.list) {
      const dt = new Date(entry.dt_txt);
      if (
        dt.getHours() === 12 &&
        (!lastDate || dt.toDateString() !== lastDate)
      ) {
        daily.push(entry);
        lastDate = dt.toDateString();
        if (daily.length === 5) break;
      }
    }

    forecastEl.innerHTML = `
      <h2 class="text-2xl mb-4">5-Day Forecast</h2>
      <div class="flex flex-wrap gap-4 justify-center">
        ${daily
          .map(
            (d) => `
            <div class="bg-blue-400 bg-opacity-30 rounded-lg p-4 w-32">
              <div class="font-bold">${new Date(
                d.dt_txt
              ).toLocaleDateString(undefined, { weekday: 'short', month: 'short', day: 'numeric' })}</div>
              <img src="https://openweathermap.org/img/wn/${
                d.weather[0].icon
              }@2x.png" alt="${d.weather[0].main}" class="mx-auto">
              <div class="font-bold">${Math.round(
                d.main.temp
              )}°C</div>
              <div class="text-xs capitalize">${d.weather[0].description}</div>
            </div>
          `
          )
          .join('')}
      </div>
    `;
  } catch (err) {
    weatherInfo.innerHTML = `<span class="text-red-400">Error: ${err.message}</span>`;
    forecastEl.textContent = '';
  }
});
