// Weather App Configuration
const CONFIG = {
  apiKey: 'cce3d0c05c88c1f0029da5f7e6c56f5d',
  baseUrl: 'https://api.openweathermap.org/data/2.5',
  units: 'metric'
};

// DOM Elements
const elements = {
  searchForm: document.getElementById('search-form'),
  cityInput: document.getElementById('city-input'),
  weatherInfo: document.getElementById('weather-info'),
  forecastEl: document.getElementById('forecast')
};

// Weather App Class
class WeatherApp {
  constructor() {
    this.initEventListeners();
  }

  initEventListeners() {
    elements.searchForm.addEventListener('submit', (e) => this.handleSearch(e));
  }

  async handleSearch(e) {
    e.preventDefault();
    const city = elements.cityInput.value.trim();

    if (!this.validateInput(city)) return;

    this.showLoading();

    try {
      const [weatherData, forecastData] = await Promise.all([
        this.fetchWeatherData(city),
        this.fetchForecastData(city)
      ]);

      this.displayWeather(weatherData);
      this.displayForecast(forecastData);
    } catch (error) {
      this.handleError(error);
    }
  }

  validateInput(city) {
    if (city.length === 0) {
      this.showError('Please enter a city name');
      return false;
    }
    if (city.length < 2) {
      this.showError('City name must be at least 2 characters long');
      return false;
    }
    return true;
  }

  showLoading() {
    elements.weatherInfo.innerHTML = `
      <div class="flex items-center justify-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white mr-3"></div>
        <span>Loading weather data...</span>
      </div>
    `;
    elements.forecastEl.innerHTML = '';
  }

  async fetchWeatherData(city) {
    const response = await fetch(
      `${CONFIG.baseUrl}/weather?q=${encodeURIComponent(city)}&appid=${CONFIG.apiKey}&units=${CONFIG.units}`
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `City "${city}" not found`);
    }

    return response.json();
  }

  async fetchForecastData(city) {
    const response = await fetch(
      `${CONFIG.baseUrl}/forecast?q=${encodeURIComponent(city)}&appid=${CONFIG.apiKey}&units=${CONFIG.units}`
    );

    if (!response.ok) {
      throw new Error('Forecast data not available');
    }

    return response.json();
  }

  displayWeather(weatherData) {
    const { name, sys, weather, main, wind } = weatherData;

    elements.weatherInfo.innerHTML = `
      <div class="mb-4 px-2">
        <!-- City Name - Mobile Responsive -->
        <div class="text-2xl sm:text-3xl md:text-4xl font-bold text-center mb-4">${name}, ${sys.country}</div>

        <!-- Main Weather Display - Mobile Responsive -->
        <div class="flex flex-col sm:flex-row items-center justify-center gap-4 mb-4">
          <img class="h-16 sm:h-20 md:h-24"
               src="https://openweathermap.org/img/wn/${weather[0].icon}@4x.png"
               alt="${weather[0].description}"
               onerror="this.style.display='none'">
          <div class="text-center sm:text-left">
            <div class="text-4xl sm:text-5xl md:text-6xl font-extrabold">${Math.round(main.temp)}°C</div>
            <div class="text-lg sm:text-xl capitalize text-blue-100">${weather[0].description}</div>
          </div>
        </div>

        <!-- Weather Details - Mobile Responsive Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-4 text-center text-sm sm:text-base">
          <div class="bg-blue-400 bg-opacity-20 rounded-lg p-2 sm:p-3">
            <div class="text-blue-200 text-xs">Humidity</div>
            <div class="font-bold">💧 ${main.humidity}%</div>
          </div>
          <div class="bg-blue-400 bg-opacity-20 rounded-lg p-2 sm:p-3">
            <div class="text-blue-200 text-xs">Wind Speed</div>
            <div class="font-bold">🌪️ ${Math.round(wind?.speed || 0)} m/s</div>
          </div>
          <div class="bg-blue-400 bg-opacity-20 rounded-lg p-2 sm:p-3">
            <div class="text-blue-200 text-xs">Feels Like</div>
            <div class="font-bold">🌡️ ${Math.round(main.feels_like)}°C</div>
          </div>
        </div>
      </div>
    `;
  }

  displayForecast(forecastData) {
    const dailyForecasts = this.processForecastData(forecastData.list);

    elements.forecastEl.innerHTML = `
      <div class="px-2">
        <h2 class="text-xl sm:text-2xl font-bold mb-4 text-center">5-Day Forecast</h2>

        <!-- Horizontal Scrolling Container - Samsung Style -->
        <div class="relative">
          <div id="forecast-scroll-container" class="flex overflow-x-auto scrollbar-hide gap-3 pb-2 snap-x snap-mandatory scroll-smooth" style="scrollbar-width: none; -ms-overflow-style: none; -webkit-overflow-scrolling: touch;">
            ${dailyForecasts.map(forecast => this.createForecastCard(forecast)).join('')}
          </div>

          <!-- Custom Horizontal Scroll Bar Indicator -->
          <div class="flex justify-center mt-3">
            <div class="relative w-20 sm:w-24 h-1.5 bg-blue-400 bg-opacity-30 rounded-full overflow-hidden">
              <div id="scroll-thumb" class="absolute top-0 left-0 h-full bg-white bg-opacity-90 rounded-full transition-transform duration-150 ease-out" style="width: 40%; transform: translateX(0%);"></div>
            </div>
          </div>
        </div>
      </div>
    `;

    // Add enhanced touch scrolling after DOM is updated
    setTimeout(() => {
      this.enhanceForecastScrolling();
      this.initScrollIndicator();
    }, 100);
  }

  processForecastData(forecastList) {
    const daily = [];
    let lastDate = '';

    // Get all available daily forecasts from the API (usually 5 days)
    for (const entry of forecastList) {
      const dt = new Date(entry.dt_txt);
      const dateString = dt.toDateString();

      // Get midday forecast for each day
      if (dt.getHours() === 12 && dateString !== lastDate) {
        daily.push(entry);
        lastDate = dateString;
        if (daily.length === 5) break; // Only get 5 real days
      }
    }

    return daily; // Return only real forecast data
  }



  createForecastCard(forecast) {
    const date = new Date(forecast.dt_txt);
    const dayName = date.toLocaleDateString(undefined, {
      weekday: 'short'
    });
    const dayDate = date.toLocaleDateString(undefined, {
      month: 'short',
      day: 'numeric'
    });

    return `
      <div class="forecast-card flex-shrink-0 bg-blue-400 bg-opacity-30 rounded-xl p-3 sm:p-4 w-24 sm:w-28 md:w-32 text-center hover:bg-opacity-50 transition-all duration-200 snap-start">
        <!-- Day Name -->
        <div class="font-bold text-xs sm:text-sm text-blue-100 mb-1">${dayName}</div>
        <div class="text-xs text-blue-200 mb-2">${dayDate}</div>

        <!-- Weather Icon -->
        <img src="https://openweathermap.org/img/wn/${forecast.weather[0].icon}@2x.png"
             alt="${forecast.weather[0].main}"
             class="mx-auto w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 mb-2"
             onerror="this.style.display='none'">

        <!-- Temperature -->
        <div class="font-bold text-sm sm:text-base md:text-lg text-white mb-1">${Math.round(forecast.main.temp)}°C</div>

        <!-- High/Low Temps -->
        <div class="text-xs text-blue-200">
          <span class="block">H: ${Math.round(forecast.main.temp_max)}°</span>
          <span class="block">L: ${Math.round(forecast.main.temp_min)}°</span>
        </div>

        <!-- Weather Description (hidden on very small screens) -->
        <div class="hidden sm:block text-xs capitalize text-blue-100 mt-1 leading-tight">${forecast.weather[0].description}</div>
      </div>
    `;
  }

  handleError(error) {
    console.error('Weather App Error:', error);

    let errorMessage = 'An unexpected error occurred';

    if (error.message.includes('not found')) {
      errorMessage = 'City not found. Please check the spelling and try again.';
    } else if (error.message.includes('network')) {
      errorMessage = 'Network error. Please check your internet connection.';
    } else if (error.message) {
      errorMessage = error.message;
    }

    this.showError(errorMessage);
  }

  showError(message) {
    elements.weatherInfo.innerHTML = `
      <div class="text-red-400 bg-red-100 bg-opacity-20 rounded-lg p-4">
        <div class="flex items-center">
          <span class="text-2xl mr-2">⚠️</span>
          <span>${message}</span>
        </div>
      </div>
    `;
    elements.forecastEl.innerHTML = '';
  }

  enhanceForecastScrolling() {
    const scrollContainer = document.getElementById('forecast-scroll-container');
    if (!scrollContainer) return;

    let isScrolling = false;
    let startX = 0;
    let scrollLeft = 0;

    // Enhanced touch scrolling for better mobile experience
    scrollContainer.addEventListener('touchstart', (e) => {
      isScrolling = true;
      startX = e.touches[0].pageX - scrollContainer.offsetLeft;
      scrollLeft = scrollContainer.scrollLeft;
      scrollContainer.style.cursor = 'grabbing';
    }, { passive: true });

    scrollContainer.addEventListener('touchmove', (e) => {
      if (!isScrolling) return;
      const x = e.touches[0].pageX - scrollContainer.offsetLeft;
      const walk = (x - startX) * 1.2; // Reduced scroll speed for smoother feel
      scrollContainer.scrollLeft = scrollLeft - walk;

      // Manually trigger scroll indicator update
      this.updateScrollIndicatorManually();
    }, { passive: true });

    scrollContainer.addEventListener('touchend', () => {
      isScrolling = false;
      scrollContainer.style.cursor = 'grab';

      // Snap to nearest card after touch ends
      this.snapToNearestCard(scrollContainer);
    }, { passive: true });

    // Mouse support for desktop testing
    scrollContainer.addEventListener('mousedown', (e) => {
      isScrolling = true;
      startX = e.pageX - scrollContainer.offsetLeft;
      scrollLeft = scrollContainer.scrollLeft;
      scrollContainer.style.cursor = 'grabbing';
      e.preventDefault();
    });

    scrollContainer.addEventListener('mousemove', (e) => {
      if (!isScrolling) return;
      e.preventDefault();
      const x = e.pageX - scrollContainer.offsetLeft;
      const walk = (x - startX) * 1.5; // Smoother mouse scrolling
      scrollContainer.scrollLeft = scrollLeft - walk;

      // Manually trigger scroll indicator update
      this.updateScrollIndicatorManually();
    });

    scrollContainer.addEventListener('mouseup', () => {
      isScrolling = false;
      scrollContainer.style.cursor = 'grab';
      this.snapToNearestCard(scrollContainer);
    });

    scrollContainer.addEventListener('mouseleave', () => {
      isScrolling = false;
      scrollContainer.style.cursor = 'grab';
    });

    // Set initial cursor
    scrollContainer.style.cursor = 'grab';
  }

  snapToNearestCard(container) {
    const firstCard = container.querySelector('.forecast-card');
    if (!firstCard) return;

    const cardWidth = firstCard.offsetWidth + 12; // card width + gap
    const scrollPosition = container.scrollLeft;
    const nearestCard = Math.round(scrollPosition / cardWidth);
    const targetPosition = nearestCard * cardWidth;

    // Only snap if we're not already close to the target
    if (Math.abs(scrollPosition - targetPosition) > 5) {
      container.scrollTo({
        left: targetPosition,
        behavior: 'smooth'
      });
    }
  }

  initScrollIndicator() {
    // Wait a bit longer to ensure DOM is fully rendered
    setTimeout(() => {
      const scrollContainer = document.getElementById('forecast-scroll-container');
      const scrollThumb = document.getElementById('scroll-thumb');

      if (!scrollContainer || !scrollThumb) {
        console.log('Scroll elements not found, retrying...');
        // Retry once more
        setTimeout(() => this.initScrollIndicator(), 500);
        return;
      }

      const updateScrollIndicator = () => {
        const scrollLeft = scrollContainer.scrollLeft;
        const scrollWidth = scrollContainer.scrollWidth;
        const clientWidth = scrollContainer.clientWidth;

        // Calculate the maximum scroll distance
        const maxScroll = scrollWidth - clientWidth;

        if (maxScroll <= 0) {
          // If no scrolling is needed, hide the indicator
          scrollThumb.style.opacity = '0.3';
          scrollThumb.style.transform = 'translateX(0%)';
          return;
        }

        // Show the indicator
        scrollThumb.style.opacity = '1';

        // Calculate thumb position (0 to 60% of the track width)
        const scrollPercentage = Math.max(0, Math.min(1, scrollLeft / maxScroll));
        const maxThumbPosition = 60; // 60% of track width (since thumb is 40% wide)
        const thumbPosition = scrollPercentage * maxThumbPosition;

        scrollThumb.style.transform = `translateX(${thumbPosition}%)`;
      };

      // Store the update function for manual calls
      this.scrollIndicatorUpdate = updateScrollIndicator;

      // Update indicator on scroll
      scrollContainer.addEventListener('scroll', updateScrollIndicator, { passive: true });

      // Initial update
      updateScrollIndicator();

      // Update on window resize
      window.addEventListener('resize', updateScrollIndicator, { passive: true });

      // Update periodically to catch any missed events
      setInterval(updateScrollIndicator, 1000);

    }, 300);
  }

  updateScrollIndicatorManually() {
    if (this.scrollIndicatorUpdate) {
      this.scrollIndicatorUpdate();
    }
  }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new WeatherApp();
});

// Add some utility functions for better UX
elements.cityInput.addEventListener('keyup', (e) => {
  if (e.key === 'Enter') {
    elements.searchForm.dispatchEvent(new Event('submit'));
  }
});

// Clear previous results when user starts typing
elements.cityInput.addEventListener('input', () => {
  if (elements.cityInput.value.trim() === '') {
    elements.weatherInfo.innerHTML = '';
    elements.forecastEl.innerHTML = '';
  }
});
