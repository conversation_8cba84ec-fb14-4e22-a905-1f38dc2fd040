const CONFIG = {
  apiKey: 'cce3d0c05c88c1f0029da5f7e6c56f5d',
  baseUrl: 'https://api.openweathermap.org/data/2.5',
  units: 'metric'
};

const elements = {
  searchForm: document.getElementById('search-form'),
  cityInput: document.getElementById('city-input'),
  weatherInfo: document.getElementById('weather-info'),
  forecastEl: document.getElementById('forecast')
};

class WeatherApp {
  constructor() {
    this.initEventListeners();
  }

  initEventListeners() {
    elements.searchForm.addEventListener('submit', (e) => this.handleSearch(e));
  }

  async handleSearch(e) {
    e.preventDefault();
    const city = elements.cityInput.value.trim();

    if (!this.validateInput(city)) return;

    this.showLoading();

    try {
      const [weatherData, forecastData] = await Promise.all([
        this.fetchWeatherData(city),
        this.fetchForecastData(city)
      ]);

      this.displayWeather(weatherData);
      this.displayForecast(forecastData);
    } catch (error) {
      this.handleError(error);
    }
  }

  validateInput(city) {
    if (city.length === 0) {
      this.showError('Please enter a city name');
      return false;
    }
    if (city.length < 2) {
      this.showError('City name must be at least 2 characters long');
      return false;
    }
    return true;
  }

  showLoading() {
    elements.weatherInfo.innerHTML = `
      <div class="flex items-center justify-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white mr-3"></div>
        <span>Loading weather data...</span>
      </div>
    `;
    elements.forecastEl.innerHTML = '';
  }

  async fetchWeatherData(city) {
    const response = await fetch(
      `${CONFIG.baseUrl}/weather?q=${encodeURIComponent(city)}&appid=${CONFIG.apiKey}&units=${CONFIG.units}`
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `City "${city}" not found`);
    }

    return response.json();
  }

  async fetchForecastData(city) {
    const response = await fetch(
      `${CONFIG.baseUrl}/forecast?q=${encodeURIComponent(city)}&appid=${CONFIG.apiKey}&units=${CONFIG.units}`
    );

    if (!response.ok) {
      throw new Error('Forecast data not available');
    }

    return response.json();
  }

  displayWeather(weatherData) {
    const { name, sys, weather, main, wind } = weatherData;

    elements.weatherInfo.innerHTML = `
      <div class="mb-4 px-2">
        <!-- City Name - Mobile Responsive -->
        <div class="text-2xl sm:text-3xl md:text-4xl font-bold text-center mb-4">${name}, ${sys.country}</div>

        <!-- Main Weather Display - Mobile Responsive -->
        <div class="flex flex-col sm:flex-row items-center justify-center gap-4 mb-4">
          <img class="h-16 sm:h-20 md:h-24"
               src="https://openweathermap.org/img/wn/${weather[0].icon}@4x.png"
               alt="${weather[0].description}"
               onerror="this.style.display='none'">
          <div class="text-center sm:text-left">
            <div class="text-4xl sm:text-5xl md:text-6xl font-extrabold">${Math.round(main.temp)}°C</div>
            <div class="text-lg sm:text-xl capitalize text-blue-100">${weather[0].description}</div>
          </div>
        </div>

        <!-- Weather Details - Mobile Responsive Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-4 text-center text-sm sm:text-base">
          <div class="bg-blue-400 bg-opacity-20 rounded-lg p-2 sm:p-3">
            <div class="text-blue-200 text-xs">Humidity</div>
            <div class="font-bold">💧 ${main.humidity}%</div>
          </div>
          <div class="bg-blue-400 bg-opacity-20 rounded-lg p-2 sm:p-3">
            <div class="text-blue-200 text-xs">Wind Speed</div>
            <div class="font-bold">🌪️ ${Math.round(wind?.speed || 0)} m/s</div>
          </div>
          <div class="bg-blue-400 bg-opacity-20 rounded-lg p-2 sm:p-3">
            <div class="text-blue-200 text-xs">Feels Like</div>
            <div class="font-bold">🌡️ ${Math.round(main.feels_like)}°C</div>
          </div>
        </div>
      </div>
    `;
  }

  displayForecast(forecastData) {
    const dailyForecasts = this.processForecastData(forecastData.list);

    elements.forecastEl.innerHTML = `
      <div class="px-2">
        <h2 class="text-xl sm:text-2xl font-bold mb-4 text-center">5-Day Forecast</h2>

        <!-- Horizontal Scrolling Container - Samsung Style -->
        <div class="relative">
          <div id="forecast-scroll-container" class="flex overflow-x-auto scrollbar-hide gap-3 pb-2 snap-x snap-mandatory scroll-smooth pl-2 pr-6" style="scrollbar-width: none; -ms-overflow-style: none; -webkit-overflow-scrolling: touch;">
            ${dailyForecasts.map(forecast => this.createForecastCard(forecast)).join('')}
            <!-- Spacer to ensure last card is fully visible -->
            <div class="flex-shrink-0 w-4"></div>
          </div>

          <!-- Custom Horizontal Scroll Bar Indicator -->
          <div class="flex justify-center mt-3">
            <div class="relative w-20 sm:w-24 h-1.5 bg-blue-400 bg-opacity-30 rounded-full overflow-hidden">
              <div id="scroll-thumb" class="absolute top-0 left-0 h-full bg-white bg-opacity-90 rounded-full transition-transform duration-150 ease-out" style="width: 40%; transform: translateX(0%);"></div>
            </div>
          </div>
        </div>
      </div>
    `;

    setTimeout(() => {
      this.enhanceForecastScrolling();
      this.initScrollIndicator();
    }, 100);
  }

  processForecastData(forecastList) {
    const daily = [];
    let lastDate = '';

    for (const entry of forecastList) {
      const dt = new Date(entry.dt_txt);
      const dateString = dt.toDateString();

      if (dt.getHours() === 12 && dateString !== lastDate) {
        daily.push(entry);
        lastDate = dateString;
        if (daily.length === 5) break;
      }
    }

    return daily;
  }



  createForecastCard(forecast) {
    const date = new Date(forecast.dt_txt);
    const dayName = date.toLocaleDateString(undefined, {
      weekday: 'short'
    });
    const dayDate = date.toLocaleDateString(undefined, {
      month: 'short',
      day: 'numeric'
    });

    return `
      <div class="forecast-card flex-shrink-0 bg-blue-400 bg-opacity-30 rounded-xl p-3 sm:p-4 w-24 sm:w-28 md:w-32 text-center hover:bg-opacity-50 transition-all duration-200 snap-start">
        <!-- Day Name -->
        <div class="font-bold text-xs sm:text-sm text-blue-100 mb-1">${dayName}</div>
        <div class="text-xs text-blue-200 mb-2">${dayDate}</div>

        <!-- Weather Icon -->
        <img src="https://openweathermap.org/img/wn/${forecast.weather[0].icon}@2x.png"
             alt="${forecast.weather[0].main}"
             class="mx-auto w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 mb-2"
             onerror="this.style.display='none'">

        <!-- Temperature -->
        <div class="font-bold text-sm sm:text-base md:text-lg text-white mb-1">${Math.round(forecast.main.temp)}°C</div>

        <!-- High/Low Temps -->
        <div class="text-xs text-blue-200">
          <span class="block">H: ${Math.round(forecast.main.temp_max)}°</span>
          <span class="block">L: ${Math.round(forecast.main.temp_min)}°</span>
        </div>

        <!-- Weather Description (hidden on very small screens) -->
        <div class="hidden sm:block text-xs capitalize text-blue-100 mt-1 leading-tight">${forecast.weather[0].description}</div>
      </div>
    `;
  }

  handleError(error) {
    console.error('Weather App Error:', error);

    let errorMessage = 'An unexpected error occurred';

    if (error.message.includes('not found')) {
      errorMessage = 'City not found. Please check the spelling and try again.';
    } else if (error.message.includes('network')) {
      errorMessage = 'Network error. Please check your internet connection.';
    } else if (error.message) {
      errorMessage = error.message;
    }

    this.showError(errorMessage);
  }

  showError(message) {
    elements.weatherInfo.innerHTML = `
      <div class="text-red-400 bg-red-100 bg-opacity-20 rounded-lg p-4">
        <div class="flex items-center">
          <span class="text-2xl mr-2">⚠️</span>
          <span>${message}</span>
        </div>
      </div>
    `;
    elements.forecastEl.innerHTML = '';
  }

  enhanceForecastScrolling() {
    const scrollContainer = document.getElementById('forecast-scroll-container');
    if (!scrollContainer) return;

    let isScrolling = false;
    let startX = 0;
    let scrollLeft = 0;
    let lastX = 0;
    let lastTime = 0;
    let velocity = 0;
    let momentumId = null;

    scrollContainer.addEventListener('touchstart', (e) => {
      isScrolling = true;
      startX = e.touches[0].pageX - scrollContainer.offsetLeft;
      lastX = startX;
      scrollLeft = scrollContainer.scrollLeft;
      lastTime = Date.now();
      velocity = 0;

      if (momentumId) {
        cancelAnimationFrame(momentumId);
        momentumId = null;
      }

      scrollContainer.style.cursor = 'grabbing';
      scrollContainer.style.scrollBehavior = 'auto';
    }, { passive: true });

    scrollContainer.addEventListener('touchmove', (e) => {
      if (!isScrolling) return;

      const currentTime = performance.now();
      const currentX = e.touches[0].pageX - scrollContainer.offsetLeft;
      const deltaX = currentX - lastX;
      const deltaTime = currentTime - lastTime;

      if (deltaTime > 0) {
        const newVelocity = deltaX / deltaTime;
        velocity = velocity * 0.8 + newVelocity * 0.2;
      }

      const walk = (currentX - startX) * 1.0;
      const newScrollLeft = scrollLeft - walk;

      requestAnimationFrame(() => {
        scrollContainer.scrollLeft = newScrollLeft;
        this.updateScrollIndicatorManually();
      });

      lastX = currentX;
      lastTime = currentTime;
    }, { passive: true });

    scrollContainer.addEventListener('touchend', () => {
      isScrolling = false;
      scrollContainer.style.cursor = 'grab';
      scrollContainer.style.scrollBehavior = 'smooth'; 
      this.applyMomentum(scrollContainer, velocity);
    }, { passive: true });

    let mouseVelocity = 0;
    let mouseLastX = 0;
    let mouseLastTime = 0;

    scrollContainer.addEventListener('mousedown', (e) => {
      isScrolling = true;
      startX = e.pageX - scrollContainer.offsetLeft;
      mouseLastX = startX;
      scrollLeft = scrollContainer.scrollLeft;
      mouseLastTime = Date.now();
      mouseVelocity = 0;

      if (momentumId) {
        cancelAnimationFrame(momentumId);
        momentumId = null;
      }

      scrollContainer.style.cursor = 'grabbing';
      scrollContainer.style.scrollBehavior = 'auto';
      e.preventDefault();
    });

    scrollContainer.addEventListener('mousemove', (e) => {
      if (!isScrolling) return;
      e.preventDefault();

      const currentTime = Date.now();
      const currentX = e.pageX - scrollContainer.offsetLeft;
      const deltaX = currentX - mouseLastX;
      const deltaTime = currentTime - mouseLastTime;

      if (deltaTime > 0) {
        mouseVelocity = deltaX / deltaTime;
      }

      const walk = (currentX - startX) * 1.0; 
      scrollContainer.scrollLeft = scrollLeft - walk;

      mouseLastX = currentX;
      mouseLastTime = currentTime;

      this.updateScrollIndicatorManually();
    });

    scrollContainer.addEventListener('mouseup', () => {
      isScrolling = false;
      scrollContainer.style.cursor = 'grab';
      scrollContainer.style.scrollBehavior = 'smooth';

      this.applyMomentum(scrollContainer, mouseVelocity);
    });

    scrollContainer.addEventListener('mouseleave', () => {
      isScrolling = false;
      scrollContainer.style.cursor = 'grab';
      scrollContainer.style.scrollBehavior = 'smooth';
    });

    scrollContainer.style.cursor = 'grab';
  }

  applyMomentum(container, velocity) {
    const friction = 0.92;
    const minVelocity = 0.05;
    let currentVelocity = velocity * 50; 

    if (Math.abs(currentVelocity) < minVelocity) {
      setTimeout(() => this.snapToNearestCard(container), 100);
      return;
    }

    const startTime = performance.now();

    const animate = (currentTime) => {
      const deltaTime = currentTime - startTime;

      currentVelocity *= friction;

      const newScrollLeft = container.scrollLeft - currentVelocity;
      const maxScroll = container.scrollWidth - container.clientWidth;

      if (newScrollLeft < 0) {
        container.scrollLeft = 0;
        currentVelocity *= -0.3; 
      } else if (newScrollLeft > maxScroll) {
        container.scrollLeft = maxScroll;
        currentVelocity *= -0.3; 
      } else {
        container.scrollLeft = newScrollLeft;
      }

      this.updateScrollIndicatorManually();

      if (Math.abs(currentVelocity) > minVelocity && deltaTime < 3000) {
        requestAnimationFrame(animate);
      } else {
        setTimeout(() => this.snapToNearestCard(container), 150);
      }
    };

    requestAnimationFrame(animate);
  }

  snapToNearestCard(container) {
    const firstCard = container.querySelector('.forecast-card');
    if (!firstCard) return;

    const cardWidth = firstCard.offsetWidth + 12; 
    const scrollPosition = container.scrollLeft;
    const containerWidth = container.clientWidth;
    const totalScrollWidth = container.scrollWidth;
    const maxScroll = totalScrollWidth - containerWidth;

    const nearestCard = Math.round(scrollPosition / cardWidth);
    let targetPosition = Math.max(0, nearestCard * cardWidth);

    targetPosition = Math.min(targetPosition, maxScroll);

    if (Math.abs(scrollPosition - targetPosition) > 3) {
      this.smoothScrollTo(container, targetPosition);
    }
  }

  smoothScrollTo(container, targetPosition) {
    const startPosition = container.scrollLeft;
    const distance = targetPosition - startPosition;
    const duration = Math.min(400, Math.abs(distance) * 2); 
    const startTime = performance.now();

    const easeOutQuart = (t) => 1 - Math.pow(1 - t, 4);

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const easedProgress = easeOutQuart(progress);

      const currentPosition = startPosition + (distance * easedProgress);
      container.scrollLeft = currentPosition;

      requestAnimationFrame(() => {
        this.updateScrollIndicatorManually();
      });

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }

  initScrollIndicator() {
    setTimeout(() => {
      const scrollContainer = document.getElementById('forecast-scroll-container');
      const scrollThumb = document.getElementById('scroll-thumb');

      if (!scrollContainer || !scrollThumb) {
        console.log('Scroll elements not found, retrying...');
        setTimeout(() => this.initScrollIndicator(), 500);
        return;
      }

      const updateScrollIndicator = () => {
        const scrollLeft = scrollContainer.scrollLeft;
        const scrollWidth = scrollContainer.scrollWidth;
        const clientWidth = scrollContainer.clientWidth;

        const maxScroll = scrollWidth - clientWidth;

        if (maxScroll <= 0) {
          scrollThumb.style.opacity = '0.3';
          scrollThumb.style.transform = 'translateX(0%)';
          return;
        }

        scrollThumb.style.opacity = '1';

        const scrollPercentage = Math.max(0, Math.min(1, scrollLeft / maxScroll));
        const maxThumbPosition = 60;
        const thumbPosition = scrollPercentage * maxThumbPosition;

        scrollThumb.style.transform = `translateX(${thumbPosition}%)`;
      };

      this.scrollIndicatorUpdate = updateScrollIndicator;

      scrollContainer.addEventListener('scroll', updateScrollIndicator, { passive: true });

      updateScrollIndicator();

      window.addEventListener('resize', updateScrollIndicator, { passive: true });

      setInterval(updateScrollIndicator, 1000);

    }, 300);
  }

  updateScrollIndicatorManually() {
    if (this.scrollIndicatorUpdate && !this.indicatorUpdatePending) {
      this.indicatorUpdatePending = true;
      requestAnimationFrame(() => {
        this.scrollIndicatorUpdate();
        this.indicatorUpdatePending = false;
      });
    }
  }
}

document.addEventListener('DOMContentLoaded', () => {
  new WeatherApp();
});

elements.cityInput.addEventListener('keyup', (e) => {
  if (e.key === 'Enter') {
    elements.searchForm.dispatchEvent(new Event('submit'));
  }
});

elements.cityInput.addEventListener('input', () => {
  if (elements.cityInput.value.trim() === '') {
    elements.weatherInfo.innerHTML = '';
    elements.forecastEl.innerHTML = '';
  }
});
